<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\NewCancelRefund;
use App\Models\AdminComission;
use App\Models\Campaign;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\RatingReview;
use App\Models\RequestTask;
use App\Models\User;
use App\Models\Invoice;
use App\Models\SocialConnect;
use App\Notifications\CancelRefund;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ActiveCampaignsController extends Controller
{
    /**
     * Active campaigns view for the influencers
     */
    protected function myActiveCampaignsInfluencer($review = false, $complaint = false) {
        $influencerData = InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id',
            '=',
            'influencer_details.id'
        )
        ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
        ->leftjoin(
            'influencer_request_accepts',
            'influencer_request_accepts.influencer_request_detail_id',
            '=',
            'influencer_request_details.id'
        )
        ->select(
            'influencer_request_details.*',
            'influencer_details.id as i_id',
            'influencer_details.user_id as i_user_id',
            'influencer_request_accepts.request',
            'influencer_request_accepts.request_time',
            'influencer_request_accepts.request_time_accept',
            'influencer_request_accepts.id as influencer_request_accept_id',
            'influencer_request_accepts.created_at as accept_time'
        )
        ->where('influencer_details.user_id', Auth::id())
        ->orderBy('influencer_request_details.id', 'desc');

        $influencerData = $influencerData->get();

        foreach ($influencerData as $row) {
            $requested_time = InfluencerRequestTime::where('influencer_request_accept_id', $row->influencer_request_accept_id)->latest()->first();
            $row->requested_time =   $requested_time;

            $row->tasks  = RequestTask::where('influencer_request_detail_id', $row->compaign_id)->get();
        }

        $campaignRequestTime = CampaignRequestTime::first();

        return view('front-user.pages.influencer.active-campaigns', compact('influencerData', 'campaignRequestTime', 'review'));
    }

    /**
     * Active campaigns view for the brands
     */
    protected function myActiveCampaignsBrand($review = false, $complaint = false) {
        $activeCampaignsCount = 0;

        $influencerCampaignDetails = InfluencerRequestDetail::join(
            'influencer_details', 'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id'
        )
        ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
        ->select(
            'influencer_request_details.*',
            'influencer_details.id as i_id',
            'influencer_details.user_id as i_user_id',
            'influencer_request_accepts.request',
            'influencer_request_accepts.id as influencer_request_accept_id',
            DB::raw('COUNT(influencer_request_accepts.id) AS total_count'),
            DB::raw('SUM(influencer_request_accepts.request) AS accept_request'),
            DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count')
        )
        ->where('influencer_request_details.user_id', Auth::id())
        ->groupBy('influencer_request_details.compaign_id')
        ->orderBy('influencer_request_details.id', 'desc')
        ->get();

        foreach ($influencerCampaignDetails as &$influencerCampaignDetail) {
            $influencerCampaignDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerCampaignDetail->campaign_id)->get();

            $influencerDetails =  InfluencerDetail::leftjoin('influencer_request_details', function ($join) use ($influencerCampaignDetail) {
                $join->on('influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')->where('influencer_request_details.compaign_id', $influencerCampaignDetail->campaign_id);
            })
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_new_prices',  'advertising_method_new_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select(
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_details.*',
                'social_connects.media',
                'social_connects.url',
                'social_connects.picture',
                'social_connects.name as username',
                'social_connects.followers',
                'advertising_method_new_prices.*',
                'hashtags.tags',
                'influencer_request_details.advertising',
                'influencer_request_details.id as request_status',
                'influencer_request_details.is_paused as is_paused',
                'influencer_request_accepts.id as accept_id',
                'influencer_request_accepts.request as accept_request'
            )
            ->where('social_connects.media', $influencerCampaignDetail->media);

            if (isset($influencerCampaignDetail->media)) {
                $influencerDetails->where('advertising_method_new_prices.media', $influencerCampaignDetail->media);
            }

            if (isset($influencerCampaignDetail->category_id)) {
                $condition = " 1 = 1 ";
                $subject_con_arr = [];

                foreach ($influencerCampaignDetail->category_id as $cat) {
                    if ($cat != '') {
                        $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                    }
                }

                if (count($subject_con_arr) > 0) {
                    $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
                }

                $influencerDetails->whereRaw($condition);
            }

            $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')
                ->where('users.status', 1)
                ->groupBy('influencer_details.id')
                ->get();

            foreach ($influencerDetails as &$influencerDetail) {
                // influencer hashtags
                $influencerDetail->tags =  Hashtag::where('user_id', $influencerDetail->i_user_id)->get('tags');

                $category = Category::where('id', $influencerDetail->category_id)->get('name');
                $influencerDetail->category =   $category->implode('name', ',');
            }

            $influencerCampaignDetail->influencerslist = $influencerDetails;
            $influencerCampaignDetail->tasks = RequestTask::where(
                'influencer_request_detail_id', $influencerCampaignDetail->compaign_id
            )
            ->get();

            $influencerCampaignDetail->campaign_details = Campaign::where(
                'campaign_id', $influencerCampaignDetail->compaign_id
            )
            ->first();

            $influencerCampaignDetail->platform_invoices = Invoice::where(
                'campaign_id', $influencerCampaignDetail->campaign_id
            )
            ->where('payment_type', 'Platform_Fee')
            ->get();

            $influencerCampaignDetail->influencer_count = 0;
            $influencerCampaignDetail->count_influencer_finished = 0;
            if ($influencerCampaignDetail->invoice_id != null) {
                if ($influencerCampaignDetail->finish != '1' && $influencerCampaignDetail->status != 'Cancelled') {
                    $activeCampaignsCount += 1;

                    // TODO first database query to InfluencerRequestDetail
                    $influencerRequestDetailInfCount = \App\Models\InfluencerRequestDetail::where(
                        'compaign_id', $influencerCampaignDetail->campaign_id
                    )
                    ->where('status', null)
                    ->get();

                    foreach ($influencerRequestDetailInfCount as $tmpInfluencerRequestDetail) {
                        if (
                            (
                                isset($tmpInfluencerRequestDetail->influencer_request_accepts->request) &&
                                $tmpInfluencerRequestDetail->influencer_request_accepts->request == 1
                            ) || !isset($tmpInfluencerRequestDetail->influencer_request_accepts->request)
                        ) {
                            $influencerCampaignDetail->influencer_count += 1;
                        }
                    }
                }
            }

            // TODO redundant database access? Can't reuse the above one?
            $influencerRequestDetails = InfluencerRequestDetail::where(
                'compaign_id', $influencerCampaignDetail->campaign_id
            )
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id',
                '=',
                'influencer_request_details.id',
            )
            ->select(
                'influencer_request_details.*',
                'influencer_request_accepts.id as influencer_request_accept_id',
                'influencer_request_accepts.request',
            )
            ->get();

            foreach ($influencerRequestDetails as &$influencerRequestDetail) {
                // TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel
                if (empty($influencerRequestDetail->influencerdetails)) {
                    continue;
                }

                $socialLink = SocialConnect::where(
                    'user_id',
                    $influencerRequestDetail->influencerdetails->user->id,
                )
                ->where('media', $influencerCampaignDetail->media)
                ->first();

                // Used in front-user.pages.market-step-detail-influencer below
                $influencerRequestDetail->social_link = $socialLink;

                if ($influencerRequestDetail->status == '0' || $influencerRequestDetail->invoice_id == null) {
                    $influencerCampaignDetail->count_influencer_finished += 1;
                } else {
                    if (isset($influencerRequestDetail->influencer_request_accepts->rating_reviews)) {
                        $influencerCampaignDetail->count_influencer_finished += 1;
                    } elseif (
                        isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                        $influencerRequestDetail->influencer_request_accepts->complaints->status != 'Inprogress'
                    ) {
                        $influencerCampaignDetail->count_influencer_finished += 1;
                    } elseif ($influencerRequestDetail->refund_reason != '') {
                        $influencerCampaignDetail->count_influencer_finished += 1;
                    } elseif ($influencerRequestDetail->finish == 1 || $influencerRequestDetail->review == 1) {
                        $influencerCampaignDetail->count_influencer_finished += 1;
                    }
                }
            }

            // Check if the campaign has already been finished
            // TODO there is bug in the timer_finished logic.
            // ************************
            // $influencerCampaignDetail->timer_finished = false;
            // if ($influencerCampaignDetail->count_influencer_finished == $influencerRequestDetails->count() && // Finish count is equials to number of influencers
            //     !(
            //         isset($influencerCampaignDetail->influencer_request_accepts->complaints) && // And there is no complaints
            //         $influencerCampaignDetail->influencer_request_accepts->complaints->status == 'Inprogress'
            //     )
            // ) {
            //     $influencerCampaignDetail->timer_finished = true;
            // }

            // Check if the campaign can be finished
            $influencerCampaignDetail->can_be_finished = false;
            if (
                $influencerCampaignDetail->count_influencer_finished == $influencerRequestDetails->count() &&
                !(
                    isset($influencerCampaignDetail->influencer_request_accepts->complaints) &&
                    $influencerCampaignDetail->influencer_request_accepts->complaints->status == 'Inprogress'
                )
            ) {
                $influencerCampaignDetail->can_be_finished = true;
            } elseif (
                $influencerCampaignDetail->is_paused == 1 ||
                (
                    isset($influencerCampaignDetail->influencer_request_accepts->complaints) &&
                    $influencerCampaignDetail->influencer_request_accepts->complaints->status == 'Inprogress'
                )
            ) {
                $influencerCampaignDetail->can_be_finished = false;
            } else {
                $influencerCampaignDetail->can_be_finished = false;
            }

            $influencerCampaignDetail->influencer_request_details = $influencerRequestDetails;

            $influencerCampaignDetail->current_phase = '';
            if (is_null($influencerCampaignDetail->social_post_id) || $influencerCampaignDetail->social_post_id == '') {
                $influencerCampaignDetail->influencer_has_submitted = false;
            } else {
                $influencerCampaignDetail->influencer_has_submitted = true;
            }
        } // end of loop foreach ($influencerCampaignDetails as &$influencerCampaignDetail) {

        $AdminComission = AdminComission::first();
        $campaignRequestTime = CampaignRequestTime::first();

        if ($review != false) {
            $review = RatingReview::whereId($review)->first();
        }

        $hasComplaint = $complaint;
        return view(
            'front-user.pages.brand.active-campaigns',
            compact(
                'influencerCampaignDetails',
                'AdminComission',
                'campaignRequestTime',
                'review',
                'complaint',
                'hasComplaint',
                'activeCampaignsCount'
            )
        );
    }

    public function myActiveCampaings($review = false, $complaint = false)
    {
        if (Auth::user()->user_type == 'influencer') {
            return $this->myActiveCampaignsInfluencer($review, $complaint);
        } else {
            return $this->myActiveCampaignsBrand($review, $complaint);
        }
    }

    public function cancelCampaignByInfluencer($influencerRequestDetailId)
    {
        var_dump("keep calm and say hakuna matata***********");
        // Find the influencer request detail with related data
        $influencerRequestDetail = InfluencerRequestDetail::leftjoin(
            'influencer_request_accepts',
            'influencer_request_accepts.influencer_request_detail_id', '=',
            'influencer_request_details.id'
        )
        ->select(
            'influencer_request_details.*',
            'influencer_request_accepts.request',
            'influencer_request_accepts.request_time',
            'influencer_request_accepts.request_time_accept'
        )
        ->where('influencer_request_details.id', $influencerRequestDetailId)
        ->first();

        // Check if campaign exists
        if (!$influencerRequestDetail) {
            return response()->json([
                'status' => 'error',
                'message' => 'Campaign not found.'
            ], 404);
        }

        // Use the new centralized cancelCampaign method
        $result = $influencerRequestDetail->cancelCampaign('cancelled_by_influencer', [
            'process_refund' => true,
            'adjust_price' => true,
            'remove_pause' => true,
            'send_notifications' => true,
            'metadata' => [
                'controller_method' => 'cancelCampaignByInfluencer',
                'user_initiated' => true,
                'request_id' => request()->id ?? null
            ]
        ]);

        print_r($result);
        exit;

        // Handle the result
        if ($result['success']) {
            \Log::info('Campaign cancelled successfully by influencer via controller', [
                'influencer_request_detail_id' => $influencerRequestDetailId,
                'campaign_id' => $influencerRequestDetail->compaign_id,
                'refund_processed' => $result['refund_processed'],
                'price_adjusted' => $result['price_adjusted'],
                'pause_removed' => $result['pause_removed'],
                'notifications_sent' => $result['notifications_sent']
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Campaign cancelled successfully.',
                'refund_processed' => $result['refund_processed'],
                'refund_id' => $result['refund_id'] ?? null
            ]);
        } else {
            // Handle different error scenarios
            $statusCode = 500;
            $message = 'Something went wrong. Please refresh the page and try again. If the problem persists, please contact support.';

            switch ($result['error_code'] ?? null) {
                case 'ALREADY_CANCELLED':
                    $statusCode = 400;
                    $message = 'This campaign has already been cancelled.';
                    break;
                case 'INVALID_REASON':
                    $statusCode = 400;
                    $message = 'Invalid cancellation reason provided.';
                    break;
                case 'SYSTEM_ERROR':
                    // Keep default message and status code
                    break;
            }

            \Log::error('Failed to cancel campaign by influencer via controller', [
                'influencer_request_detail_id' => $influencerRequestDetailId,
                'campaign_id' => $influencerRequestDetail->compaign_id ?? null,
                'error_code' => $result['error_code'] ?? null,
                'error_message' => $result['message'] ?? null,
                'error_details' => $result['error_details'] ?? null
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $message,
                'error_code' => $result['error_code'] ?? null
            ], $statusCode);
        }
    }
}
